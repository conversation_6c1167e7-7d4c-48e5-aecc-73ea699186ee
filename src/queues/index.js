export class SendToQueue {
  static async email(env, data, options = {}) {
    return env.EMAIL_QUEUE.send({ type: 'email', ...data }, options);
  }

  static async salseforce(env, data, options = {}) {
    return env.SALESFORCE_QUEUE.send({ type: 'salesforce', ...data }, options);
  }

  static async admin(env, data, options = {}) {
    return env.ADMIN_QUEUE.send({ type: 'admin', ...data }, options);
  }
}
