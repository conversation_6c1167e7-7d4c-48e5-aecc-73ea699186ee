import { Hono } from 'hono';
import { WorkerEntrypoint } from 'cloudflare:workers';

import { completeAppHandlers } from './handlers/app/complete-app';
import { createtAppHandlers } from './handlers/app/create-app';
import { createAppFasttrackHandlers } from './handlers/app/create-app-fasttrack';
import { editAppHandlers } from './handlers/app/edit-app';
import { getAppHandlers } from './handlers/app/get-app';
import { pandadocStatusHandlers } from './handlers/app/pandadoc-status';
import { signAppHandlers } from './handlers/app/sign-app';
import { startAppHandlers } from './handlers/app/start-app';
import { submitAppHandlers } from './handlers/app/submit-app';
import { errorHandler } from './handlers/error/error-handler';
import { exportDataHandlers } from './handlers/export-data';
import { validateRepHandlers } from './handlers/validate-rep';
import { logErrorToKV } from './kv/logs';
import { attachTimestampMiddleware } from './middlewares/attach-timestamp';
import { apiBodyLimitMiddleware } from './middlewares/body-limit';
import { corsMiddleware } from './middlewares/cors';
import { rateLimitMiddleware } from './middlewares/rate-limit';
import { adminQueueHandler } from './queues/admin';
import { emailQueueHandler } from './queues/email';
import { salesforceQueueHandler } from './queues/salesforce';

import { RoundRobin } from './classes/round-robin';
import { Agents } from './classes/agents';

const app = new Hono();

app.use('*', async (c, next) => {
  const rawBody = await c.req.text();
  c.set('rawBody', rawBody);
  c.req.body = () => Promise.resolve(new TextEncoder().encode(rawBody)); // restore body for next handlers
  await next();
});

app.use('*', corsMiddleware);
app.use('*', rateLimitMiddleware);
app.use('*', apiBodyLimitMiddleware);
app.use('*', attachTimestampMiddleware);

app.get('/', (c) => c.text('OK'));

app.post('/app', ...createtAppHandlers);
app.post('/app/fasttrack', ...createAppFasttrackHandlers);
app.get('/app/:uuid', ...getAppHandlers);

app.post('/app/:uuid/submit', ...submitAppHandlers);
app.post('/app/:uuid/edit', ...editAppHandlers);
app.post('/app/:uuid/start', ...startAppHandlers);
app.post('/app/:uuid/sign', ...signAppHandlers);
app.post('/app/:uuid/complete', ...completeAppHandlers);

app.get('/app/:uuid/pandadoc/status', ...pandadocStatusHandlers);

app.get('/reps/:rep/validate', ...validateRepHandlers);

app.get('/export', ...exportDataHandlers);

app.notFound((c) => c.text('Not Found', 404));

app.onError(errorHandler);

export default class extends WorkerEntrypoint {
  // Handle HTTP Requests
  async fetch(request) {
    return app.fetch(request, this.env, this.ctx);
  }
  // Handle Queue Messages (consumer)
  async queue(batch, env) {
    // disable queue while running tests
    const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;
    if (IS_TESTING) {
      return;
    }
    console.log(`[QUEUE] Processing ${batch.messages.length} messages from queue: ${batch.queue}`);

    const messagePromises = batch.messages.map(async (message) => {
      console.log(`[QUEUE] Processing message:`, JSON.stringify(message));
      try {
        // Parse the message body if it's a string
        const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

        let handler;
        if (batch.queue.includes('-email')) {
          handler = emailQueueHandler;
          console.log(`[QUEUE] Processing email queue message`);
        } else if (batch.queue.includes('-salesforce')) {
          handler = salesforceQueueHandler;
          console.log(`[QUEUE] Processing salesforce queue message`);
        } else if (batch.queue.includes('-admin')) {
          handler = adminQueueHandler;
          console.log(`[QUEUE] Processing admin queue message`);
        } else {
          console.warn(`[QUEUE] Unknown queue: ${batch.queue}`);
          message.ack();
          return;
        }

        await handler(data, env);

        // Acknowledge the message was processed successfully
        message.ack();
      } catch (error) {
        const { errorId } = await logErrorToKV(env, error, {
          source: error?.source || 'queueHandler',
          statusCode: error?.statusCode || 500,
        });

        console.error(`[QUEUE] Error processing message:${errorId} - ${error?.message}`);
        // Retry the message
        message.retry();
      }
    });

    await Promise.all(messagePromises);
  }

  // RPC Methods
  async ping() {
    return 'pong';
  }

  async RoundRobin(method, ...args) {
    console.log('[RPC]', `RoundRobin.${method}`, ...args);
    if (typeof RoundRobin[method] !== 'function') throw Error(`Method ${method} not found`);
    return RoundRobin[method](this.env, ...args);
  }

  async Agents(method, ...args) {
    console.log('[RPC]', `Agents.${method}`, ...args);
    if (typeof Agents[method] !== 'function') throw Error(`Method ${method} not found`);
    return Agents[method](this.env, ...args);
  }
}
