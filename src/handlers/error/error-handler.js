import { logErrorToKV } from '../../kv/logs.js';
import { AppError } from '../../utils/helpers.js';

export const errorHandler = async (err, c) => {
  const headers = Object.fromEntries(c.req.raw.headers);
  const contentType = c.req.raw.headers.get('content-type');
  const bodyUsed = c.req.raw.bodyUsed;

  let body = null;
  if (contentType?.includes('json')) {
    body = bodyUsed ? await c.req.bodyCache.json.catch(() => 'INVALID_JSON') : await c.req.json().catch(() => 'INVALID_JSON');
  }
  if (!body) {
    body = bodyUsed ? await c.req.bodyCache.text : await c.req.text().catch(() => null);
  }


  console.log(err);

  const { errorId } = await logErrorToKV(c.env, err, {
    headers,
    cf: c.req.raw.cf,
    method: c.req.raw.method,
    requestBody: body || 'EMPTY',
    source: err?.source || 'errorHandler',
    statusCode: err?.statusCode || 500,
  });

  if (err instanceof AppError) {
    console.error(err);
    return c.json({ errorId, error: err.message }, err.statusCode);
  } else {
    console.error(err);
    return c.json({ errorId, error: 'Internal Server Error' }, 500);
  }
};
