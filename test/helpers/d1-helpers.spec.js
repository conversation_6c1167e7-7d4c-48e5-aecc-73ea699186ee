import { env } from 'cloudflare:test';
import { beforeEach, describe, expect, it } from 'vitest';
import { ApplicationD1 } from '../../src/db/applications';
import { generateApplicationData, generatePrequalData } from '../setup';
import { safeUTMObject } from '../../src/utils/helpers';

describe('ApplicationD1 Class', () => {
  beforeEach(async () => {
    // Set version for tests
    env.VERSION = '1.0.0';
  });

  describe('ApplicationD1.create', () => {
    it('creates a new application in D1', async () => {
      const uuid = 'test-uuid-1';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        agent: {
          name: 'Test Agent',
          email: '<EMAIL>',
        },
        utm: safeUTMObject({
          utm_source:
            'START_AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA_END_CUTOFFFROMHERE',
          utm_medium: 'test',
        }),
        meta: {
          initiated: {
            timestamp: new Date().toISOString(),
          },
        },
        fastTrack: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const result = await ApplicationD1.create(env, applicationData);

      expect(result).toEqual(
        expect.objectContaining({
          uuid,
          status: 'PREQUAL_APPROVED',
          domain: 'app.pinnaclefunding.com',
          fastTrack: false,
        })
      );
    });
  });

  describe('ApplicationD1.update', () => {
    it('updates an existing application in D1', async () => {
      const uuid = 'test-uuid-2';
      const initialData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Create initial application
      await ApplicationD1.create(env, initialData);

      // Update the application
      const fieldsToUpdate = {
        status: 'APP_STARTED',
        started_at: new Date().toISOString(),
      };

      const result = await ApplicationD1.update(env, uuid, fieldsToUpdate);

      expect(result.status).toBe('APP_STARTED');
      expect(result.started_at).toBeDefined();
    });
  });

  describe('ApplicationD1.get', () => {
    it('handles JSON fields correctly', async () => {
      const uuid = 'test-uuid-3';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'APP_SUBMITTED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        applicationFields: generateApplicationData(),
        agent: {
          name: 'Test Agent',
          email: '<EMAIL>',
        },
        pandadoc: {
          document: {
            id: 'test-doc-id',
          },
        },
        fastTrack: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await ApplicationD1.create(env, applicationData);
      const retrieved = await ApplicationD1.get(env, uuid);

      expect(retrieved.preQualifyFields).toEqual(applicationData.preQualifyFields);
      expect(retrieved.applicationFields).toEqual(applicationData.applicationFields);
      expect(retrieved.agent).toEqual(applicationData.agent);
      expect(retrieved.pandadoc).toEqual(applicationData.pandadoc);
      expect(retrieved.fastTrack).toBe(true);
    });

    it('retrieves an application from D1', async () => {
      const uuid = 'test-uuid-4';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await ApplicationD1.create(env, applicationData);
      const retrieved = await ApplicationD1.get(env, uuid);

      expect(retrieved).toEqual(
        expect.objectContaining({
          uuid,
          status: 'PREQUAL_APPROVED',
          domain: 'app.pinnaclefunding.com',
          fastTrack: false,
        })
      );
      expect(retrieved.preQualifyFields).toEqual(applicationData.preQualifyFields);
    });

    it('returns null for non-existent application', async () => {
      const result = await ApplicationD1.get(env, 'non-existent-uuid');
      expect(result).toBeNull();
    });

    it('returns null for empty uuid', async () => {
      const result = await ApplicationD1.get(env, '');
      expect(result).toBeNull();
    });

    it('handles boolean conversion for fastTrack correctly', async () => {
      const uuid1 = 'test-uuid-5';
      const uuid2 = 'test-uuid-6';

      // Test fastTrack: true
      await ApplicationD1.create(env, {
        uuid: uuid1,
        version: env.VERSION,
        status: 'PREQUAL_FAST_TRACK',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Test fastTrack: false
      await ApplicationD1.create(env, {
        uuid: uuid2,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const app1 = await ApplicationD1.get(env, uuid1);
      const app2 = await ApplicationD1.get(env, uuid2);

      expect(app1.fastTrack).toBe(true);
      expect(app2.fastTrack).toBe(false);
    });
  });
});
